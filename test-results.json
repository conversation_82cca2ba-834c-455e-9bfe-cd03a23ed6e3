{"config": {"configFile": "/home/<USER>/DEV/lib2docScrape/playwright.config.js", "rootDir": "/home/<USER>/DEV/lib2docScrape/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/home/<USER>/DEV/lib2docScrape/tests/e2e/global-setup.js", "globalTeardown": "/home/<USER>/DEV/lib2docScrape/tests/e2e/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/home/<USER>/DEV/lib2docScrape/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/home/<USER>/DEV/lib2docScrape/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/home/<USER>/DEV/lib2docScrape/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/home/<USER>/DEV/lib2docScrape/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/home/<USER>/DEV/lib2docScrape/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/home/<USER>/DEV/lib2docScrape/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/home/<USER>/DEV/lib2docScrape/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/home/<USER>/DEV/lib2docScrape/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/home/<USER>/DEV/lib2docScrape/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/home/<USER>/DEV/lib2docScrape/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/home/<USER>/DEV/lib2docScrape/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Tablet", "name": "Tablet", "testDir": "/home/<USER>/DEV/lib2docScrape/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 8, "webServer": {"command": "python run_gui.py", "port": 60643, "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: Process from config.webServer was not able to start. Exit code: 1", "stack": "Error: Process from config.webServer was not able to start. Exit code: 1"}], "stats": {"startTime": "2025-06-12T07:43:34.148Z", "duration": 279.96699999999987, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}